# 歌谱投影控制器V1.9.8 - 代码拆分重构计划

## 项目概述
当前main.py文件有6116行代码，需要将其拆分成多个模块，每个模块控制在4000行左右。

## 重构目标
1. **代码量控制**: 主文件控制在4000行以内
2. **模块化设计**: 功能清晰分离，低耦合高内聚
3. **向后兼容**: 保持现有功能和API不变
4. **可维护性**: 提升代码可读性和维护性

## 拆分策略

### 阶段一: 核心模块拆分（优先级：高）

#### 1. UI组件模块 - `ui_components.py`
**预估行数**: 1000-1200行
**负责功能**:
- [ ] 菜单栏创建和管理 (`create_menu()`)
- [ ] 项目列表树组件 (`create_project_list()`)
- [ ] 搜索组件创建和事件处理
- [ ] 关键帧指示器组件 (`create_keyframe_indicator()`, `update_keyframe_indicators()`)
- [ ] 预览线组件 (`create_preview_lines()`, `update_preview_lines()`)
- [ ] 字体和DPI设置 (`init_dpi_scaling()`, `load_font_settings()`)
- [ ] 样式初始化 (`init_search_styles()`)

**主要类**:
```python
class UIComponents:
    def __init__(self, main_app)
    def create_menu_bar()
    def create_project_tree()
    def create_search_panel()
    def create_keyframe_indicators()
    def create_preview_lines()
    def update_fonts()
```

#### 2. 图片处理模块 - `image_processor.py`
**预估行数**: 1200-1500行
**负责功能**:
- [ ] 图片加载和缓存管理 (`ImageCache`, `load_image()`)
- [ ] 图片缩放和显示逻辑 (`update_image()`)
- [ ] 黄字效果处理 (`apply_yellow_text_effect()`)
- [ ] 反色效果处理 (`simple_invert_image()`)
- [ ] 原图模式处理（与yuantu.py集成）
- [ ] 图片保存功能 (`save_yellow_text_image()`)
- [ ] 滚动处理和同步 (`on_scroll()`, `smooth_scroll_to()`)

**主要类**:
```python
class ImageProcessor:
    def __init__(self, main_app)
    def load_image(path)
    def apply_effects(image, effect_type)
    def resize_image(image, width, height)
    def update_display()
    
class ImageCache:
    # 保持现有实现
```

#### 3. 投影显示模块 - `projection_manager.py`
**预估行数**: 800-1000行
**负责功能**:
- [ ] 投影窗口管理 (`toggle_projection()`, `open_projection()`)
- [ ] 多屏幕支持 (`show_on_second_screen()`)
- [ ] 屏幕信息获取 (`get_monitor_info()`)
- [ ] 投影同步逻辑 (`sync_projection_screen_absolute()`)
- [ ] 投影屏幕更新 (`update_projection()`, `update_second_screen()`)
- [ ] 全局热键管理（与keyboard库集成）

**主要类**:
```python
class ProjectionManager:
    def __init__(self, main_app)
    def toggle_projection()
    def create_projection_window()
    def manage_multi_screen()
    def sync_displays()
    def setup_global_hotkeys()
```

#### 4. 关键帧控制模块 - `keyframe_controller.py`
**预估行数**: 1200-1500行
**负责功能**:
- [ ] 关键帧CRUD操作 (`add_keyframe()`, `clear_keyframes()`)
- [ ] 关键帧导航 (`step_to_next_keyframe()`, `step_to_prev_keyframe()`)
- [ ] 平滑滚动实现 (`smooth_scroll_to()`)
- [ ] 自动播放控制集成
- [ ] 关键帧指示器更新协调
- [ ] 智能跳转逻辑 (`_should_auto_jump_to_first()`)

**主要类**:
```python
class KeyframeController:
    def __init__(self, main_app)
    def add_keyframe(position)
    def navigate_keyframes(direction)
    def smooth_scroll(target_position)
    def auto_jump_logic()
```

### 阶段二: 数据和配置模块拆分（优先级：中）

#### 5. 数据管理模块 - `data_manager.py`
**预估行数**: 800-1000行
**负责功能**:
- [ ] 项目数据加载 (`load_projects()`)
- [ ] 文件导入处理 (`import_files()`, `import_folder()`)
- [ ] 项目树管理 (`update_project_tree()`)
- [ ] 数据库初始化 (`init_database()`)
- [ ] 拖拽处理 (`on_drop()`)
- [ ] 项目删除和重组 (`delete_project()`, `reorganize_projects()`)

**主要类**:
```python
class DataManager:
    def __init__(self, main_app)
    def load_projects_from_db()
    def import_project_files()
    def manage_project_tree()
    def handle_drag_drop()
```

#### 6. 配置管理模块 - `config_manager.py`
**预估行数**: 600-800行
**负责功能**:
- [ ] 配置文件读写 (`load_config()`, `save_config()`)
- [ ] UI设置管理 (`load_ui_settings()`, `save_ui_settings()`)
- [ ] 黄字颜色管理 (`load_yellow_color_settings()`)
- [ ] 显示模式设置 (`save_display_mode_settings()`)
- [ ] 字体设置管理 (`load_font_settings()`, `update_all_fonts()`)

**主要类**:
```python
class ConfigManager:
    def __init__(self, main_app)
    def load_application_config()
    def save_application_config()
    def manage_ui_settings()
    def handle_color_settings()
```

### 阶段三: 事件和交互模块拆分（优先级：中）

#### 7. 事件处理模块 - `event_handler.py`
**预估行数**: 800-1000行
**负责功能**:
- [ ] 鼠标事件处理 (`on_mousewheel()`, `on_indicator_click()`)
- [ ] 键盘事件处理 (`on_key_press()`)
- [ ] 窗口事件处理 (`on_window_resize()`)
- [ ] 拖拽排序事件 (拖拽相关事件处理)
- [ ] 右键菜单事件
- [ ] 搜索事件处理

**主要类**:
```python
class EventHandler:
    def __init__(self, main_app)
    def handle_mouse_events()
    def handle_keyboard_events()
    def handle_window_events()
    def handle_drag_events()
```

### 重构后的main.py结构

**预估行数**: 2000-2500行
**保留功能**:
```python
class ImageProjector:
    def __init__(self, root):
        # 初始化各个模块
        self.ui_components = UIComponents(self)
        self.image_processor = ImageProcessor(self)
        self.projection_manager = ProjectionManager(self)
        self.keyframe_controller = KeyframeController(self)
        self.data_manager = DataManager(self)
        self.config_manager = ConfigManager(self)
        self.event_handler = EventHandler(self)
        
        # 保留核心状态变量
        # 保留主要的协调逻辑
        
    # 保留对外接口方法（确保向后兼容）
    def load_image(self, path):
        return self.image_processor.load_image(path)
        
    def toggle_projection(self):
        return self.projection_manager.toggle_projection()
        
    # 其他核心协调方法...
```

## 实施计划

### 第一周: UI组件模块
- [ ] 创建 `ui_components.py`
- [ ] 迁移菜单创建相关代码
- [ ] 迁移项目树相关代码
- [ ] 迁移搜索组件代码
- [ ] 测试UI组件功能完整性

### 第二周: 图片处理模块
- [ ] 创建 `image_processor.py`
- [ ] 迁移图片加载和缓存代码
- [ ] 迁移图片效果处理代码
- [ ] 迁移滚动和显示逻辑
- [ ] 测试图片显示功能

### 第三周: 投影显示模块
- [ ] 创建 `projection_manager.py`
- [ ] 迁移投影窗口管理代码
- [ ] 迁移多屏幕支持代码
- [ ] 迁移同步逻辑代码
- [ ] 测试投影功能

### 第四周: 关键帧控制模块
- [ ] 创建 `keyframe_controller.py`
- [ ] 迁移关键帧操作代码
- [ ] 迁移平滑滚动代码
- [ ] 迁移导航逻辑代码
- [ ] 测试关键帧功能

### 第五周: 数据管理模块
- [ ] 创建 `data_manager.py`
- [ ] 迁移项目加载代码
- [ ] 迁移文件导入代码
- [ ] 迁移拖拽处理代码
- [ ] 测试数据管理功能

### 第六周: 配置和事件模块
- [ ] 创建 `config_manager.py`
- [ ] 创建 `event_handler.py`
- [ ] 迁移配置管理代码
- [ ] 迁移事件处理代码
- [ ] 全面集成测试

### 第七周: 整合和优化
- [ ] 重构main.py，整合所有模块
- [ ] 优化模块间接口
- [ ] 性能测试和优化
- [ ] 完整功能测试
- [ ] 文档更新

## 技术考虑

### 模块间通信
- 使用主应用实例作为中心协调器
- 通过事件机制减少模块间直接依赖
- 保持清晰的接口定义

### 向后兼容性
- 保持所有公有方法和属性的接口不变
- 使用代理模式转发调用到相应模块
- 维持现有的数据库结构

### 性能优化
- 模块延迟初始化
- 避免循环导入
- 保持图片缓存的高效性

### 测试策略
- 每个模块独立测试
- 集成测试覆盖模块间交互
- 回归测试确保功能完整性

## 预期收益

1. **代码可维护性**: 每个模块职责明确，便于维护和扩展
2. **开发效率**: 团队成员可以并行开发不同模块
3. **测试覆盖**: 模块化便于单元测试和集成测试
4. **性能优化**: 模块化加载可以减少启动时间
5. **功能扩展**: 新功能可以作为独立模块添加

## 风险控制

1. **备份策略**: 每个阶段保留原始代码备份
2. **渐进迁移**: 逐步迁移功能，确保每步可验证
3. **测试验证**: 每个模块迁移后进行功能测试
4. **回滚机制**: 如果出现问题可以快速回滚到稳定版本

## 成功标准

- [ ] 主文件代码量控制在4000行以内
- [ ] 所有现有功能正常工作
- [ ] 性能不低于原始版本
- [ ] 代码结构清晰，易于维护
- [ ] 具备良好的扩展性

---

**注意**: 此计划需要根据实际代码结构和依赖关系进行调整。建议在开始实施前进行详细的代码依赖分析。
