# 歌谱投影控制器V1.9.8 - Python模块功能文档

## 项目概述

歌谱投影控制器是一个基于Python和Tkinter开发的专业级图片投影管理应用程序，主要用于教堂、音乐会等场景的歌谱图片投影显示。该系统支持多屏投影、图片管理、关键帧控制、自动播放等功能。

## 系统架构

```
歌谱投影控制器
├── main.py              # 主程序界面和核心控制逻辑
├── database_manager.py  # 数据库连接管理和优化
├── keytime.py          # 关键帧时间录制和自动播放
├── yuantu.py           # 原图模式管理和相似图片切换
└── 配置文件
    ├── config.json     # 应用配置文件
    └── pyimages.db     # SQLite数据库文件
```

## 核心模块功能详解

### 1. main.py - 主程序模块

**核心类：** `ImageProjector`

**主要功能：**

#### 1.1 用户界面管理
- **菜单栏控制：** 导入、投影、同步、返回、原图、缩放、变色、反色等按钮
- **项目列表：** 支持拖拽排序的文件夹和图片管理
- **搜索功能：** 实时搜索图片和文件夹，支持范围筛选
- **关键帧控制：** 加帧、清帧、上帧、下帧、播放、录制等控制按钮

#### 1.2 图片显示与处理
- **多格式支持：** JPG、PNG、BMP、GIF、TIF等图片格式
- **智能缩放：** 自适应屏幕尺寸，支持手动缩放
- **图片效果：** 
  - 黄字模式（黑底黄字效果）
  - 普通反色
  - 原图模式切换
- **图片缓存：** LRU缓存机制优化图片加载性能

#### 1.3 多屏投影系统
- **双屏支持：** 主屏操控，副屏投影
- **屏幕选择：** 自动识别多显示器，支持手动选择投影屏幕
- **同步滚动：** 主副屏绝对位置同步
- **投影优化：** 智能缩放策略，确保最佳显示效果

#### 1.4 关键帧管理
- **关键帧添加：** 标记图片中的重要位置
- **可视化指示器：** 右侧指示器显示所有关键帧位置
- **预览线：** 实时显示当前帧和下一帧位置
- **平滑滚动：** 支持0-10秒可调的平滑滚动时间

#### 1.5 数据库集成
- **项目管理：** 文件夹和图片的层级管理
- **配置存储：** 界面设置、字体大小、颜色配置等
- **关键帧数据：** 关键帧位置和时间信息存储
- **原图标记：** 原图模式标记管理

#### 1.6 全局热键支持
- **PageUp/PageDown：** 关键帧切换
- **ESC：** 结束投影
- **投影状态控制：** 仅在投影开启时启用全局热键

**关键方法：**
```python
def load_image(path)              # 图片加载和缓存
def update_image()                # 图片显示更新
def toggle_projection()           # 投影开关控制
def sync_projection_screen_absolute() # 绝对位置同步
def add_keyframe()                # 添加关键帧
def smooth_scroll_to()            # 平滑滚动
```

### 2. database_manager.py - 数据库管理模块

**核心类：** `DatabaseManager`

**主要功能：**

#### 2.1 高性能数据库连接管理
- **连接复用：** 减少连接创建/销毁开销
- **线程安全：** 支持多线程环境下的安全访问
- **连接池机制：** 自动管理数据库连接生命周期

#### 2.2 性能优化设置
- **WAL模式：** 启用Write-Ahead Logging提升并发性能
- **缓存优化：** 10MB页面缓存，256MB内存映射I/O
- **同步模式：** NORMAL同步模式平衡性能与安全
- **查询优化：** 自动优化查询计划器

#### 2.3 事务批处理支持
- **批量操作：** 支持多个SQL操作的原子性执行
- **事务嵌套：** 支持嵌套事务的安全管理
- **回滚机制：** 异常情况下的自动回滚

#### 2.4 向后兼容API
- **统一接口：** 与原有代码无缝兼容
- **上下文管理：** 支持with语句的资源管理
- **错误处理：** 完善的异常捕获和处理

**关键方法：**
```python
def get_connection()              # 获取数据库连接
def transaction()                 # 事务上下文管理
def execute_batch()              # 批量操作执行
def safe_execute()               # 安全执行SQL
def optimize_database()          # 数据库优化
```

### 3. keytime.py - 关键帧时间管理模块

包含两个核心类：`KeyTimeRecorder` 和 `AutoPlayer`

#### 3.1 KeyTimeRecorder - 时间录制器

**主要功能：**

##### 关键帧时间录制
- **开始/停止录制：** 智能录制关键帧之间的停留时间
- **实时记录：** 自动计算和记录每个关键帧的停留时间
- **时间序列存储：** 将录制的时间数据存储到数据库

##### 原图模式时间录制
- **图片切换录制：** 录制相似图片之间的切换时间
- **循环检测：** 自动检测循环完成并停止录制
- **标记类型支持：** 支持循环和顺序两种播放模式

##### 数据管理
- **时间数据清除：** 支持清除指定图片的时间数据
- **时间修正：** 实时修正播放过程中的手动操作时间
- **数据验证：** 验证时间序列的有效性

#### 3.2 AutoPlayer - 自动播放器

**主要功能：**

##### 关键帧自动播放
- **按时播放：** 根据录制的时间序列自动播放关键帧
- **播放控制：** 支持播放、暂停、停止、跳转等操作
- **循环播放：** 支持设定播放次数或无限循环
- **速度调节：** 0.1x-5.0x播放速度调节

##### 原图模式自动播放
- **相似图片播放：** 自动在相似图片之间切换
- **时间同步：** 根据录制的切换时间进行播放
- **模式切换：** 支持循环和顺序播放模式

##### 智能优化
- **时间修正：** 实时记录手动操作，修正播放时间
- **跳转优化：** 短时间停留自动使用直接跳转
- **播放统计：** 记录播放次数、时间等统计信息

**关键方法：**
```python
def start_recording()            # 开始时间录制
def record_keyframe_timing()     # 记录关键帧时间
def start_auto_play()           # 开始自动播放
def _play_next_frame()          # 播放下一帧
def record_manual_operation()    # 记录手动操作
```

### 4. yuantu.py - 原图模式管理模块

**核心类：** `YuanTuManager`

**主要功能：**

#### 4.1 原图模式控制
- **模式切换：** 原图模式和普通模式之间的切换
- **智能缩放：** 根据屏幕和图片尺寸智能调整显示效果
- **显示模式：** 支持适中和拉伸两种显示模式

#### 4.2 原图标记管理
- **标记添加/移除：** 为文件夹或图片添加原图标记
- **标记类型：** 支持循环（loop）和顺序（sequence）标记
- **标记显示：** 在项目树中显示标记状态（★/☆图标）
- **智能切换：** 根据标记状态自动切换原图模式

#### 4.3 相似图片管理
- **智能识别：** 基于文件名模式识别同一首歌的不同页面
- **名称解析：** 支持多种命名格式：
  - `第0001首 圣哉三一1`, `第0001首 圣哉三一2`
  - `001.圣哉三一歌1`, `001.圣哉三一歌2`
  - `因为有你01`, `因为有你02`
- **系列分组：** 将同一首歌的多个页面自动分组

#### 4.4 图片切换功能
- **方向键切换：** 左右方向键在相似图片间切换
- **切换模式：** 
  - 循环模式：最后一页回到第一页
  - 顺序模式：到边界时切换到下一个不同系列
- **录制集成：** 切换时自动记录时间用于录制功能

**关键方法：**
```python
def toggle_original_mode()       # 切换原图模式
def mark_as_original()          # 标记为原图
def find_similar_images()       # 查找相似图片
def is_same_song_series()       # 判断是否同一首歌
def find_and_switch_similar_image() # 切换相似图片
```

## 数据库结构

### 主要数据表：

1. **folders** - 文件夹信息
2. **images** - 图片信息
3. **keyframes** - 关键帧数据
4. **keyframe_timings** - 关键帧时间记录
5. **original_mode_timings** - 原图模式时间记录
6. **original_marks** - 原图标记
7. **manual_sort_folders** - 手动排序文件夹标记
8. **ui_settings** - 界面设置
9. **settings** - 通用设置

## 文件依赖关系

```
main.py
├── database_manager.py (数据库管理)
├── keytime.py (时间录制和播放)
├── yuantu.py (原图模式管理)
└── 第三方库
    ├── tkinter (GUI界面)
    ├── PIL (图片处理)
    ├── numpy (图像处理)
    ├── sqlite3 (数据库)
    ├── screeninfo (屏幕信息)
    ├── keyboard (全局热键)
    └── pypinyin (中文拼音排序)
```

## 主要特性

1. **专业投影：** 双屏投影，支持多显示器环境
2. **智能管理：** 自动识别图片系列，智能分组和排序
3. **关键帧控制：** 精确的关键帧定位和平滑滚动
4. **自动播放：** 基于时间录制的自动播放功能
5. **原图模式：** 智能的相似图片切换和循环播放
6. **性能优化：** 图片缓存、数据库优化、多线程处理
7. **用户友好：** 拖拽排序、实时搜索、全局热键支持
8. **可扩展性：** 模块化设计，易于维护和扩展

## 版本信息

- **当前版本：** V1.9.8
- **开发语言：** Python 3.11+
- **UI框架：** Tkinter
- **数据库：** SQLite
- **图片处理：** PIL (Pillow)
- **作者：** 静等
- **联系方式：** 18162074638

这个项目展现了完整的桌面应用程序开发实践，包含了GUI设计、数据库管理、多媒体处理、多线程编程等多个技术领域的综合应用。
