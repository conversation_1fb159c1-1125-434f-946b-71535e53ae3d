from typing import Any, Dict, Tu<PERSON>, Text

# 注音转换表
BOPOMOFO_REPLACE = ...  # type: <PERSON><PERSON>[<PERSON><PERSON>[Any]]
BOPOMOFO_TABLE = ...  # type: Dict[Text, Text]


class BopomofoConverter(object):
    def to_bopomofo(self, pinyin: Text, **kwargs: Any) -> Text: ...

    def to_bopomofo_first(self, pinyin: Text, **kwargs: Any) -> Text: ...

    def _pre_convert(self, pinyin: Text) -> Text: ...

converter = ...  # type: BopomofoConverter
